<?php

namespace App\Exceptions;

use Throwable;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use <PERSON><PERSON>\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;

class Hand<PERSON> extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        ApiException::class,
        HttpException::class,
        ValidationException::class,
        AuthorizationException::class,
        ModelNotFoundException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  \Throwable  $exception
     * @return void
     *
     * @throws \Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response. 
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($request->is('api/*'))
        {
            $code = ApiCodeEnum::ERROR;
            $message = '发生错误';
            $data = [];
            if($exception instanceof ApiException)
            {
                $code = $exception->getCode();
                $message = $exception->getMessage();
                $data = $exception->getData();
            } elseif($exception instanceof ValidationException){
                $code = ApiCodeEnum::VALIDATION_ERROR;
                $message = current(current($exception->errors()));
                // 返回422状态码用于参数验证错误
                return response(['code' => $code,'message' => $message,'data' => []], 422);
            } elseif ($exception instanceof MethodNotAllowedHttpException) {
                $code = ApiCodeEnum::METHOD_NOT_ALLOWED;
                $message = '网络请求方法错误';
            } elseif(env('APP_DEBUG')){
                $message = $exception->getMessage();
                $data = $exception->getTrace();
            }else{
                $url = null;
                $msg = null;
                $params = null;
                if(!empty($request->path())){
                    $url = $request->path().'@'.$request->ip();
                }
                if(!empty($exception->getMessage())){
                    $msg = $exception->getMessage();
                }
                if(!empty($request->all())){
                    $params = json_encode($request->all());
                }
                DB::table('exception_logs')->insert([
                    'url' => $url,
                    'msg' => $msg,
                    'params' => $params,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            if($code !== 200)
            {
                $data = [];
            }

            return response(['code' => $code,'message' => $message,'data' => $data]);
        }
        if(method_exists($exception,'getStatusCode') && $exception->getStatusCode() == 404)
        {
            return response(view('errors.404')->render(), 404);
        }
        return parent::render($request, $exception);
    }
}
